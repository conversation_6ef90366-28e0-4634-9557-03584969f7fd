package kr.wayplus.qr_hallimpark.controller.manage;

import kr.wayplus.qr_hallimpark.model.LoginAttemptLog;
import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.model.LoginUserSession;
import kr.wayplus.qr_hallimpark.service.manage.ManageUserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.util.HashMap;

/**
 * 관리자 사용자 컨트롤러
 * - 관리자 전용 로그인 페이지 및 인증 처리
 */
@Controller
@RequestMapping("/manage")
@RequiredArgsConstructor
public class ManageUserController {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ManageUserService manageUserService;

    /**
     * 관리자 로그인 페이지
     * @param error 에러 파라미터
     * @param code 에러 코드
     * @param logout 로그아웃 파라미터
     * @param request HTTP 요청
     * @param model 모델
     * @return 관리자 로그인 페이지
     */
    @GetMapping("/login")
    public String adminLoginPage(
            @RequestParam(value = "logout", required = false) String logout,
            HttpServletRequest request,
            Model model) {
        
        logger.debug("Admin login page requested. Logout: {}", logout);

        // 이미 로그인된 관리자는 대시보드로 리다이렉트
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
            boolean isAdmin = auth.getAuthorities().stream()
                    .anyMatch(authority ->
                        authority.getAuthority().equals("ROLE_ADMIN"));

            if (isAdmin) {
                logger.debug("Admin already authenticated: {}", auth.getName());
                return "redirect:/manage";
            }
        }

        // 페이지 메타 정보 설정
        model.addAttribute("pageTitle", "관리자 로그인");
        model.addAttribute("pageDescription", "한림공원 QR 체험 관리자 시스템에 로그인하세요.");

        // 세션에서 에러 메시지 확인 (한 번만 표시됨)
        HttpSession session = request.getSession(false);
        if (session != null && session.getAttribute("adminLoginError") != null) {
            model.addAttribute("showLoginError", true);
            model.addAttribute("adminLoginError", session.getAttribute("adminLoginError"));
            model.addAttribute("adminLoginErrorCode", session.getAttribute("adminLoginErrorCode"));
            model.addAttribute("adminLoginErrorMessage", session.getAttribute("adminLoginErrorMessage"));

            logger.debug("Admin login error found in session: {}", session.getAttribute("adminLoginErrorMessage"));

            // 세션에서 에러 메시지 제거 (한 번만 표시)
            session.removeAttribute("adminLoginError");
            session.removeAttribute("adminLoginErrorCode");
            session.removeAttribute("adminLoginErrorMessage");
        } else {
            logger.debug("No admin login error in session");
        }

        // 로그아웃 메시지 처리 (에러가 없을 때만)
        if ("y".equals(logout) && !model.containsAttribute("adminLoginError")) {
            model.addAttribute("showLogoutMessage", true);
            logger.debug("Logout message will be shown");
        }
        
        // 저장된 관리자 아이디 확인 (쿠키에서)
        String savedAdminId = getSavedAdminId(request);
        if (savedAdminId != null && !savedAdminId.isEmpty()) {
            model.addAttribute("savedAdminId", savedAdminId);
            model.addAttribute("rememberChecked", true);
        }
        
        return "manage/login";
    }

    /**
     * 관리자 로그아웃 처리
     * @param request HTTP 요청
     * @param response HTTP 응답
     * @return 리다이렉트 URL
     */
    @GetMapping("/logout")
    public String adminLogout(HttpServletRequest request, HttpServletResponse response) {
        logger.debug("Admin logout requested");

        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                // 세션에서 관리자 정보 가져오기 (로그아웃 로그 저장용)
                LoginUser adminUser = (LoginUser) session.getAttribute("adminLogin");

                if (adminUser != null) {
                    // 관리자 세션 로그아웃 로그 저장
                    LoginUserSession loginUserSession = new LoginUserSession();
                    loginUserSession.setUser_email(adminUser.getUser_email());
                    loginUserSession.setLogin_session(session.getId());
                    loginUserSession.setLogout_type("ADMIN_LOGOUT");

                    manageUserService.updateAdminSessionLogout(loginUserSession);
                    logger.debug("Admin logout log saved for user: {}", adminUser.getUser_email());
                }

                // 세션에서 관리자 정보 제거
                session.removeAttribute("adminLogin");
                session.removeAttribute("loginType");
                session.invalidate();
            }

            // Spring Security 컨텍스트 클리어
            SecurityContextHolder.clearContext();

            // 관리자 아이디 저장 쿠키 삭제
            Cookie adminLoginCookie = new Cookie("hallimpark.admin.login.id", "");
            adminLoginCookie.setMaxAge(-1);
            adminLoginCookie.setPath("/");
            response.addCookie(adminLoginCookie);

            logger.debug("Admin logout completed");

        } catch (Exception e) {
            logger.error("Admin logout error: {}", e.getMessage(), e);
        }

        return "redirect:/manage/login?logout=y";
    }

    /**
     * 관리자 로그인 처리
     * @param adminEmail 관리자 이메일
     * @param adminPass 관리자 비밀번호
     * @param remember 아이디 저장 여부
     * @param request HTTP 요청
     * @param response HTTP 응답
     * @param model 모델
     * @return 리다이렉트 URL
     */
    @PostMapping("/login-progress")
    public String adminLoginProgress(
            @RequestParam("admin-email") String adminEmail,
            @RequestParam("admin-pass") String adminPass,
            @RequestParam(value = "remember", required = false) String remember,
            HttpServletRequest request,
            HttpServletResponse response,
            RedirectAttributes redirectAttributes) {

        logger.info("=== Admin login progress started ===");
        logger.info("Admin login attempt. Email: {}", adminEmail);
        logger.info("Request method: {}", request.getMethod());
        logger.info("Request URI: {}", request.getRequestURI());

        try {
            // 관리자 사용자 조회
            LoginUser adminUser = manageUserService.findAdminUserByUsername(adminEmail);

            if (adminUser == null) {
                logger.warn("Admin user not found: {}", adminEmail);
                saveLoginAttemptLog(adminEmail, request, "001");
                redirectAttributes.addFlashAttribute("adminLoginError", true);
                redirectAttributes.addFlashAttribute("adminLoginErrorCode", "001");
                redirectAttributes.addFlashAttribute("adminLoginErrorMessage", "관리자 권한이 없거나 존재하지 않는 사용자입니다.");
                return "redirect:/manage/login";
            }

            // 비밀번호 검증 (세션 전달)
            HttpSession session = request.getSession();
            if (!manageUserService.verifyPassword(adminPass, adminUser.getUser_pass(), session)) {
                logger.warn("Admin password mismatch: {}", adminEmail);
                saveLoginAttemptLog(adminEmail, request, "002");
                redirectAttributes.addFlashAttribute("adminLoginError", true);
                redirectAttributes.addFlashAttribute("adminLoginErrorCode", "002");
                redirectAttributes.addFlashAttribute("adminLoginErrorMessage", "비밀번호가 일치하지 않습니다.");
                logger.debug("Added flash attributes for admin login error: adminLoginError=true, errorCode=002");
                return "redirect:/manage/login";
            }

            // 인증 성공 - 세션에 관리자 정보 저장
            adminUser.setUser_pass(""); // 보안을 위해 비밀번호 제거
            session.setAttribute("adminLogin", adminUser);
            session.setAttribute("loginType", "ADMIN");

            // Spring Security 컨텍스트에 인증 정보 설정
            UsernamePasswordAuthenticationToken authToken =
                new UsernamePasswordAuthenticationToken(adminUser, null, adminUser.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authToken);

            // 로그인 로그 저장
            saveLoginLog(adminUser, session, request);

            // 마지막 로그인 일자 업데이트
            manageUserService.updateAdminLastLoginDate(adminUser);

            // 아이디 저장 처리
            handleRememberMe(adminEmail, remember, response);

            logger.debug("Admin login success: {}", adminEmail);
            return "redirect:/manage";

        } catch (Exception e) {
            logger.error("Admin login error: {}", e.getMessage(), e);
            saveLoginAttemptLog(adminEmail, request, "999");
            redirectAttributes.addFlashAttribute("adminLoginError", true);
            redirectAttributes.addFlashAttribute("adminLoginErrorCode", "999");
            redirectAttributes.addFlashAttribute("adminLoginErrorMessage", "로그인 처리 중 오류가 발생했습니다.");
            return "redirect:/manage/login";
        }
    }



    /**
     * 저장된 관리자 아이디 조회 (쿠키에서)
     * @param request HTTP 요청
     * @return 저장된 관리자 아이디
     */
    private String getSavedAdminId(HttpServletRequest request) {
        if (request.getCookies() != null) {
            for (Cookie cookie : request.getCookies()) {
                if ("hallimpark.admin.login.id".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 로그인 시도 로그 저장
     * @param email 이메일
     * @param request HTTP 요청
     * @param errorCode 에러 코드
     */
    private void saveLoginAttemptLog(String email, HttpServletRequest request, String errorCode) {
        try {
            LoginAttemptLog attemptLog = new LoginAttemptLog();
            attemptLog.setUser_email(email);
            attemptLog.setAttempt_ip(getClientIpAddress(request));
            attemptLog.setAttempt_agent(request.getHeader("User-Agent"));
            attemptLog.setAttempt_referer(request.getHeader("Referer"));
            attemptLog.setError_code(errorCode);

            // 에러 코드에 따른 에러 메시지 설정
            String errorMessage = "";
            switch (errorCode) {
                case "001":
                    errorMessage = "관리자 권한이 없거나 존재하지 않는 사용자입니다.";
                    break;
                case "002":
                    errorMessage = "비밀번호가 일치하지 않습니다.";
                    break;
                case "999":
                    errorMessage = "로그인 처리 중 오류가 발생했습니다.";
                    break;
                default:
                    errorMessage = "로그인 중 오류가 발생했습니다.";
            }
            attemptLog.setError_message(errorMessage);

            manageUserService.writeAdminLoginAttemptLog(attemptLog);
        } catch (Exception e) {
            logger.error("Failed to save login attempt log: {}", e.getMessage(), e);
        }
    }

    /**
     * 로그인 로그 저장
     * @param user 사용자
     * @param session 세션
     * @param request HTTP 요청
     */
    private void saveLoginLog(LoginUser user, HttpSession session, HttpServletRequest request) {
        try {
            HashMap<String, String> parameterMap = new HashMap<>();
            parameterMap.put("UserEmail", user.getUser_email());
            parameterMap.put("SessionId", session.getId());
            parameterMap.put("LoginIp", getClientIpAddress(request));
            parameterMap.put("UserAgent", request.getHeader("User-Agent"));
            parameterMap.put("Referer", request.getHeader("Referer"));

            manageUserService.writeAdminLoginLog(parameterMap);
        } catch (Exception e) {
            logger.error("Failed to save login log: {}", e.getMessage(), e);
        }
    }

    /**
     * 아이디 저장 처리
     * @param email 이메일
     * @param remember 저장 여부
     * @param response HTTP 응답
     */
    private void handleRememberMe(String email, String remember, HttpServletResponse response) {
        try {
            if ("Y".equals(remember)) {
                Cookie adminLoginCookie = new Cookie("hallimpark.admin.login.id", email);
                adminLoginCookie.setMaxAge(60 * 60 * 24 * 90); // 90일
                adminLoginCookie.setPath("/");
                adminLoginCookie.setHttpOnly(true);
                response.addCookie(adminLoginCookie);
            } else {
                // 기존 쿠키 삭제
                Cookie adminLoginCookie = new Cookie("hallimpark.admin.login.id", "");
                adminLoginCookie.setMaxAge(-1);
                adminLoginCookie.setPath("/");
                response.addCookie(adminLoginCookie);
            }
        } catch (Exception e) {
            logger.error("Failed to handle remember me: {}", e.getMessage(), e);
        }
    }

    /**
     * 클라이언트 IP 주소 조회
     * @param request HTTP 요청
     * @return 클라이언트 IP 주소
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
