<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>관리자 로그인 - 한림공원 QR 체험 관리시스템</title>
    <meta name="description" content="한림공원 QR 체험 관리자 시스템에 로그인하세요.">
    <style>
        .admin-login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .admin-login-card {
            max-width: 450px;
            margin: 0 auto;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .admin-login-header {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .admin-login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        .admin-login-body {
            padding: 2.5rem;
        }
        .form-floating {
            margin-bottom: 1.5rem;
        }
        .form-floating .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            transition: all 0.3s ease;
        }
        .form-floating .form-control:focus {
            border-color: #2a5298;
            box-shadow: 0 0 0 0.2rem rgba(42, 82, 152, 0.25);
        }
        .btn-admin-login {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            border: none;
            border-radius: 12px;
            padding: 1rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .btn-admin-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(42, 82, 152, 0.4);
        }
        .btn-admin-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-admin-login:hover::before {
            left: 100%;
        }
        .remember-me {
            margin: 1.5rem 0;
        }
        .admin-login-footer {
            text-align: center;
            padding: 1.5rem 2.5rem 2.5rem;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
            border-radius: 0 0 20px 20px;
        }
        .admin-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="admin-login-container">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <div class="admin-login-card">
                            <!-- 관리자 로그인 헤더 -->
                            <div class="admin-login-header">
                                <i class="fas fa-shield-alt fa-4x mb-3"></i>
                                <h2 class="mb-2">관리자 시스템</h2>
                                <p class="mb-0 opacity-75">한림공원 QR 체험 관리</p>
                            </div>
                            
                            <!-- 관리자 로그인 폼 -->
                            <div class="admin-login-body">
                                <!-- 관리자 배지 -->
                                <div class="text-center">
                                    <span class="admin-badge">
                                        <i class="fas fa-user-shield me-2"></i>ADMIN ACCESS
                                    </span>
                                </div>
                                
                                <!-- 보안 안내 -->
                                <div class="security-notice">
                                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                                    <strong>보안 안내:</strong> 관리자 권한이 필요한 시스템입니다. 
                                    승인된 관리자만 접근 가능합니다.
                                </div>
                                
                                <!-- 성공 메시지는 alert로 표시 -->
                                <!-- 에러 메시지는 JavaScript alert로 표시 -->
                                
                                <form action="/manage/login-progress" method="post" id="adminLoginForm">
                                    <!-- 관리자 이메일 입력 -->
                                    <div class="form-floating">
                                        <input type="email"
                                               class="form-control"
                                               id="admin-email"
                                               name="admin-email"
                                               placeholder="관리자 이메일"
                                               th:value="${savedAdminId}">
                                        <label for="admin-email">
                                            <i class="fas fa-user-shield me-2"></i>관리자 이메일
                                        </label>
                                        <div class="invalid-feedback">
                                            관리자 이메일 주소를 입력해주세요.
                                        </div>
                                    </div>
                                    
                                    <!-- 비밀번호 입력 -->
                                    <div class="form-floating">
                                        <input type="password"
                                               class="form-control"
                                               id="admin-pass"
                                               name="admin-pass"
                                               placeholder="비밀번호">
                                        <label for="admin-pass">
                                            <i class="fas fa-lock me-2"></i>비밀번호
                                        </label>
                                        <div class="invalid-feedback">
                                            비밀번호를 입력해주세요.
                                        </div>
                                    </div>
                                    
                                    <!-- 아이디 저장 체크박스 -->
                                    <div class="form-check remember-me">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="remember" 
                                               name="remember" 
                                               value="Y"
                                               th:checked="${rememberChecked}">
                                        <label class="form-check-label" for="remember">
                                            <i class="fas fa-save me-1"></i>관리자 아이디 저장
                                        </label>
                                    </div>
                                    
                                    <!-- 로그인 버튼 -->
                                    <button type="submit" class="btn btn-primary btn-admin-login w-100">
                                        <i class="fas fa-sign-in-alt me-2"></i>관리자 로그인
                                    </button>
                                </form>
                            </div>
                            
                            <!-- 관리자 로그인 푸터 -->
                            <div class="admin-login-footer">
                                <div class="text-center">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        시스템 관리자 권한이 필요합니다
                                    </small>
                                </div>
                                <hr class="my-3">
                                <div class="text-center">
                                    <a th:href="@{/}" class="text-decoration-none me-3">
                                        <i class="fas fa-home me-1"></i>메인 사이트
                                    </a>
                                    <a th:href="@{/user/login}" class="text-decoration-none">
                                        <i class="fas fa-user me-1"></i>일반 로그인
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 추가 스크립트 -->
    <div layout:fragment="scripts">
        <script>
            // 폼 유효성 검사
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    console.log('Page loaded, initializing form validation');
                    var forms = document.getElementsByClassName('needs-validation');
                    console.log('Found forms:', forms.length);

                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            console.log('Form validation triggered');
                            if (form.checkValidity() === false) {
                                console.log('Form validation failed');
                                event.preventDefault();
                                event.stopPropagation();
                            } else {
                                console.log('Form validation passed');
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();
            
            // 로그인 버튼 로딩 처리
            const loginForm = document.querySelector('form');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    console.log('Form submit event triggered');
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn && this.checkValidity()) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>로그인 중...';
                        submitBtn.disabled = true;
                    }
                });
            }
            
            // 이메일 입력 시 자동 포커스 및 에러 메시지 처리
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM Content Loaded');

                // 서버에서 전달된 플래시 속성 확인 (한 번만 표시됨)
                console.log('Checking for messages...');

                // 로그인 에러 메시지 확인
                const showLoginError = /*[[${showLoginError}]]*/ false;
                console.log('showLoginError:', showLoginError);

                if (showLoginError) {
                    const errorMessage = /*[[${adminLoginErrorMessage}]]*/ '';
                    console.log('Error message:', errorMessage);
                    if (errorMessage && errorMessage.trim() !== '') {
                        alert('❌ ' + errorMessage);
                    } else {
                        console.log('Error message is empty or null');
                    }
                }

                // 로그아웃 성공 메시지 확인
                const showLogoutMessage = /*[[${showLogoutMessage}]]*/ false;
                console.log('showLogoutMessage:', showLogoutMessage);

                if (showLogoutMessage) {
                    alert('✅ 관리자 로그아웃이 완료되었습니다.');
                }

                const emailInput = document.getElementById('admin-email');
                if (!emailInput.value) {
                    emailInput.focus();
                } else {
                    document.getElementById('admin-pass').focus();
                }
            });
        </script>
    </div>
</body>
</html>
