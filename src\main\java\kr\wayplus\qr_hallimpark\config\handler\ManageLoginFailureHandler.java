package kr.wayplus.qr_hallimpark.config.handler;

import kr.wayplus.qr_hallimpark.model.LoginAttemptLog;
import kr.wayplus.qr_hallimpark.service.manage.ManageUserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 관리자 로그인 실패 핸들러
 * - 관리자 전용 로그인 실패 시 처리 로직
 */
@Component
@RequiredArgsConstructor
public class ManageLoginFailureHandler implements AuthenticationFailureHandler {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ManageUserService manageUserService;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, 
                                      AuthenticationException exception) throws IOException, ServletException {
        
        String username = request.getParameter("admin-email");
        String errorCode = determineErrorCode(exception);
        
        logger.warn("Admin login failed. Username: {}, Error: {}, Exception: {}", 
                   username, errorCode, exception.getClass().getSimpleName());

        // 로그인 시도 로그 생성
        LoginAttemptLog attemptLog = new LoginAttemptLog();
        attemptLog.setUser_email(username != null ? username : "unknown");
        attemptLog.setAttempt_ip(getClientIpAddress(request));
        attemptLog.setAttempt_agent(request.getHeader("User-Agent"));
        attemptLog.setAttempt_referer(request.getHeader("Referer"));
        attemptLog.setError_code(errorCode);
        attemptLog.setError_message(determineErrorMessage(exception));

        try {
            // 세션에서 기존 관리자 로그인 정보 제거
            HttpSession session = request.getSession();
            if (session.getAttribute("adminLogin") != null) {
                session.removeAttribute("adminLogin");
            }
            if (session.getAttribute("loginType") != null) {
                session.removeAttribute("loginType");
            }
            
            // 관리자 로그인 시도 로그 저장
            manageUserService.writeAdminLoginAttemptLog(attemptLog);
            
        } catch (Exception e) {
            logger.error("Failed to save admin login attempt log: {}", e.getMessage(), e);
        }

        // 관리자 로그인 페이지로 리다이렉트 (에러 코드 포함)
        String redirectUrl = request.getContextPath() + "/manage/login?error=y&code=" + errorCode;
        response.sendRedirect(redirectUrl);
    }

    /**
     * 예외 타입에 따른 에러 코드 결정
     * @param exception 인증 예외
     * @return 에러 코드
     */
    private String determineErrorCode(AuthenticationException exception) {
        if (exception instanceof UsernameNotFoundException) {
            return "001"; // 관리자 권한이 없거나 존재하지 않는 사용자
        } else if (exception instanceof BadCredentialsException) {
            return "002"; // 비밀번호 불일치
        } else if (exception instanceof LockedException) {
            return "003"; // 계정 잠김
        } else if (exception instanceof DisabledException) {
            return "004"; // 계정 비활성화
        } else {
            return "999"; // 기타 오류
        }
    }

    /**
     * 예외 타입에 따른 에러 메시지 결정
     * @param exception 인증 예외
     * @return 에러 메시지
     */
    private String determineErrorMessage(AuthenticationException exception) {
        if (exception instanceof UsernameNotFoundException) {
            return "관리자 권한이 없거나 존재하지 않는 사용자입니다.";
        } else if (exception instanceof BadCredentialsException) {
            return "비밀번호가 일치하지 않습니다.";
        } else if (exception instanceof LockedException) {
            return "잠긴 계정입니다.";
        } else if (exception instanceof DisabledException) {
            return "비활성화된 계정입니다.";
        } else {
            return "로그인 처리 중 오류가 발생했습니다.";
        }
    }

    /**
     * 클라이언트 IP 주소 조회
     * @param request HTTP 요청
     * @return 클라이언트 IP 주소
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
