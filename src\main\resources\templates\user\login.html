<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/user}">
<head>
    <title>로그인 - 한림공원 QR 체험</title>
    <meta name="description" content="한림공원 QR 체험 서비스에 로그인하세요.">
    <style>
        .login-container {
            min-height: 80vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            max-width: 400px;
            margin: 0 auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .login-header {
            background: linear-gradient(135deg, #2E8B57, #20B2AA);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-login {
            background: linear-gradient(135deg, #2E8B57, #20B2AA);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 139, 87, 0.3);
        }
        .remember-me {
            margin: 1rem 0;
        }
        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body class="bg-light">
    <div layout:fragment="content">
        <div class="container login-container">
            <div class="row justify-content-center w-100">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card border-0">
                        <!-- 로그인 헤더 -->
                        <div class="login-header">
                            <i class="fas fa-shield-alt fa-3x mb-3"></i>
                            <h3 class="mb-0">한림공원</h3>
                            <p class="mb-0 opacity-75">로그인</p>
                        </div>
                        
                        <!-- 로그인 폼 -->
                        <div class="login-body">
                            <!-- 성공 메시지는 alert로 표시 -->
                            <!-- 에러 메시지는 JavaScript alert로 표시 -->
                            
                            <form th:action="@{/user/login-progress}" method="post" class="needs-validation" novalidate>
                                <!-- 이메일 입력 -->
                                <div class="form-floating">
                                    <input type="email" 
                                           class="form-control" 
                                           id="user-email" 
                                           name="user-email" 
                                           placeholder="이메일 주소"
                                           th:value="${savedUserId}"
                                           required>
                                    <label for="user-email">
                                        <i class="fas fa-envelope me-2"></i>이메일 주소
                                    </label>
                                    <div class="invalid-feedback">
                                        올바른 이메일 주소를 입력해주세요.
                                    </div>
                                </div>
                                
                                <!-- 비밀번호 입력 -->
                                <div class="form-floating">
                                    <input type="password" 
                                           class="form-control" 
                                           id="user-pass" 
                                           name="user-pass" 
                                           placeholder="비밀번호"
                                           required>
                                    <label for="user-pass">
                                        <i class="fas fa-lock me-2"></i>비밀번호
                                    </label>
                                    <div class="invalid-feedback">
                                        비밀번호를 입력해주세요.
                                    </div>
                                </div>
                                
                                <!-- 아이디 저장 체크박스 -->
                                <div class="form-check remember-me">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="remember" 
                                           name="remember" 
                                           value="Y"
                                           th:checked="${rememberChecked}">
                                    <label class="form-check-label" for="remember">
                                        <i class="fas fa-save me-1"></i>아이디 저장
                                    </label>
                                </div>
                                
                                <!-- 로그인 버튼 -->
                                <button type="submit" class="btn btn-primary btn-login w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>로그인
                                </button>
                                
                                <!-- CSRF 토큰 (Spring Security) -->
                                <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>
                            </form>
                        </div>
                        
                        <!-- 로그인 푸터 -->
                        <div class="login-footer">
                            <div class="text-center">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    일반 사용자 로그인 페이지입니다
                                </small>
                            </div>
                            <hr class="my-3">
                            <div class="text-center">
                                <a th:href="@{/}" class="text-decoration-none me-3">
                                    <i class="fas fa-home me-1"></i>홈으로 돌아가기
                                </a>
                                <a th:href="@{/manage/login}" class="text-decoration-none">
                                    <i class="fas fa-shield-alt me-1"></i>관리자 로그인
                                </a>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    
    <!-- 추가 스크립트 -->
    <div layout:fragment="scripts">
        <script>
            // 폼 유효성 검사
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();
            
            // 로그인 버튼 로딩 처리
            document.querySelector('form').addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (this.checkValidity()) {
                    HallimPark.showLoading(submitBtn);
                }
            });
            
            // 이메일 입력 시 자동 포커스 및 에러 메시지 처리
            document.addEventListener('DOMContentLoaded', function() {
                // URL 파라미터에서 에러 및 성공 메시지 확인
                const urlParams = new URLSearchParams(window.location.search);
                const error = urlParams.get('error');
                const code = urlParams.get('code');
                const logout = urlParams.get('logout');

                // 에러 메시지 alert 표시
                if (error === 'y' && code) {
                    let errorMessage = '';
                    switch (code) {
                        case 'NON':
                            errorMessage = '등록된 아이디가 아닙니다.';
                            break;
                        case 'PAS':
                            errorMessage = '비밀번호가 일치하지 않습니다.';
                            break;
                        case 'LOC':
                            errorMessage = '잠긴 사용자 아이디입니다.';
                            break;
                        case 'DIS':
                            errorMessage = '휴면상태의 아이디입니다.';
                            break;
                        case 'EXD':
                            errorMessage = '만료된 아이디입니다.';
                            break;
                        case 'EXP':
                            errorMessage = '비밀번호가 만료됐습니다.';
                            break;
                        case 'expired':
                            errorMessage = '세션이 만료되었습니다. 다시 로그인해주세요.';
                            break;
                        default:
                            errorMessage = '로그인 중 오류가 발생했습니다. 다시 시도해주세요.';
                    }
                    alert('❌ ' + errorMessage);
                }

                // 로그아웃 성공 메시지 alert 표시
                if (logout === 'y') {
                    alert('✅ 성공적으로 로그아웃되었습니다.');
                }

                const emailInput = document.getElementById('user-email');
                if (!emailInput.value) {
                    emailInput.focus();
                } else {
                    document.getElementById('user-pass').focus();
                }
            });
        </script>
    </div>
</body>
</html>
