<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.qr_hallimpark.mapper.ManageUserMapper">
    
    <!-- 관리자 권한 사용자 조회 (교환권 QR 승인 가능한 사용자만) -->
    <select id="selectAdminUserByUserid" parameterType="String" resultType="LoginUser">
        SELECT user_idx, user_email, password as user_pass, name as user_name,
               contact_number, status, last_login_date, user_token_id,
               create_date, last_update_date, login_fail_count
          FROM users
         WHERE user_email = #{value}
           AND status = 'ACTIVE'
           AND delete_yn = 'N'
           AND use_yn = 'Y'
    </select>
    
    <!-- 관리자 로그인 시도 로그 저장 -->
    <insert id="insertAdminLoginAttemptLog" parameterType="LoginAttemptLog">
        INSERT INTO user_login_attempt_log
               (
                user_email, attempt_ip, attempt_agent,
                attempt_referer, attempt_time, error_code,
                attempt_type
               )
        VALUES (
                #{user_email}, #{attempt_ip}, #{attempt_agent},
                #{attempt_referer}, now(), #{error_code},
                'ADMIN'
               )
    </insert>
    
    <!-- 관리자 로그인 로그 저장 -->
    <insert id="insertAdminLoginLog" parameterType="HashMap">
        INSERT INTO user_login_session
        (user_email, login_session, login_ip,
         login_agent, login_referer, login_time, login_type)
        VALUES (#{UserEmail}, #{SessionId}, #{LoginIp},
                #{UserAgent}, #{Referer}, now(), 'ADMIN')
    </insert>
    
    <!-- 관리자 마지막 로그인 일자 업데이트 -->
    <update id="updateAdminLastLoginDate" parameterType="LoginUser">
        UPDATE users
           SET last_login_date = now(),
               last_update_date = now()
         WHERE user_email = #{user_email}
           AND status = 'ACTIVE'
           AND delete_yn = 'N'
           AND use_yn = 'Y'
    </update>
    
    <!-- 관리자 세션 로그아웃 처리 -->
    <update id="updateAdminSessionLogout" parameterType="LoginUserSession">
        UPDATE user_login_session
           SET logout_time = now(),
               logout_type = 'ADMIN_LOGOUT'
         WHERE user_email = #{user_email}
           AND login_session = #{login_session}
           AND logout_time IS NULL
    </update>
    
    <!-- 관리자 권한 사용자 수 조회 -->
    <select id="selectAdminUserCountById" parameterType="String" resultType="int">
        SELECT COUNT(*)
          FROM users
         WHERE user_email = #{value}
           AND status = 'ACTIVE'
           AND delete_yn = 'N'
           AND use_yn = 'Y'
    </select>
    
</mapper>
