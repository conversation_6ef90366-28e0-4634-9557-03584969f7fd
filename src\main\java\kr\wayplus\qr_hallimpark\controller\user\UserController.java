package kr.wayplus.qr_hallimpark.controller.user;

import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.service.UserService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

/**
 * 사용자 관련 컨트롤러
 * - 로그인, 로그아웃, 세션 관리 등
 */
@Controller
@RequestMapping("/user")
public class UserController {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * 로그인 페이지
     */
    @GetMapping("/login")
    public String loginPage(
            @RequestParam(value = "logout", required = false) String logout,
            HttpServletRequest request,
            Model model) {
        
        logger.debug("Login page requested. Logout: {}", logout);

        // 이미 로그인된 사용자는 홈으로 리다이렉트
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
            logger.debug("User already authenticated: {}", auth.getName());
            return "redirect:/";
        }

        // 페이지 메타 정보 설정
        model.addAttribute("pageTitle", "로그인");
        model.addAttribute("pageDescription", "한림공원 QR 체험 서비스에 로그인하세요.");

        // 플래시 속성에서 에러 메시지 확인 (한 번만 표시됨)
        if (model.containsAttribute("userLoginError")) {
            model.addAttribute("showLoginError", true);
            // 에러 메시지는 이미 플래시 속성으로 전달됨
        }

        // 로그아웃 메시지 처리
        if ("y".equals(logout)) {
            model.addAttribute("showLogoutMessage", true);
        }
        
        // 저장된 아이디 확인 (쿠키에서)
        String savedUserId = getSavedUserId(request);
        if (savedUserId != null && !savedUserId.isEmpty()) {
            model.addAttribute("savedUserId", savedUserId);
            model.addAttribute("rememberChecked", true);
        }
        
        return "user/login";
    }

    /**
     * 세션 만료 페이지
     */
    @GetMapping("/session-expired")
    public String sessionExpiredPage(Model model) {
        logger.debug("Session expired page requested");

        model.addAttribute("pageTitle", "세션 만료");
        model.addAttribute("pageDescription", "세션이 만료되었습니다.");

        return "user/session-expired";
    }

    /**
     * 로그아웃 처리 (GET 요청 - 직접 접근 시)
     */
    @GetMapping("/logout")
    public String logoutPage() {
        logger.debug("Logout page accessed via GET");
        return "redirect:/user/login?logout=y";
    }



    /**
     * 쿠키에서 저장된 사용자 아이디 가져오기
     */
    private String getSavedUserId(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals("qr-hallimpark.co.krlogin.id")) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}
